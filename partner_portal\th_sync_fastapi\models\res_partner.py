import logging
from odoo import models, fields, api
_logger = logging.getLogger(__name__)
class ResPartner(models.Model):
    _name = "res.partner"
    _inherit = ["res.partner", "th.intermediate.table"]

    def get_contact_data_sync(self, partner, action, th_type_sync='ontime'):
        data = {
            'th_customer_code': partner.th_customer_code if partner.th_customer_code else False,
            'name': partner.name if partner.name else False,
            'phone': partner.phone.strip() if partner.phone else False,
            'th_phone2': partner.th_phone2.strip() if partner.th_phone2 else False,
            'email': partner.email.strip() if partner.email else False,

            'th_gender': partner.th_gender if isinstance(partner.th_gender, str) else False,
            'th_birthday': partner.th_birthday.strftime('%Y-%m-%d') if partner.th_birthday else False,
            'th_place_of_birth_id': str(partner.th_place_of_birth_id.id) if partner.th_place_of_birth_id else False,

            'title': partner.title.id if partner.title else False,
            'function': partner.function.strip() if partner.function else False,
            'th_citizen_identification': partner.th_citizen_identification.strip() if partner.th_citizen_identification else False,
            'th_date_identification': partner.th_date_identification.isoformat() if partner.th_date_identification else False,
            'th_place_identification': partner.th_place_identification.strip() if partner.th_place_identification else False,
            'vat': partner.vat.strip() if partner.vat else False,
            'lang': partner.lang.strip() if partner.lang else False,
            'street': partner.street.strip() if partner.street else False,
            'th_ward_id': partner.th_ward_id.id if partner.th_ward_id else False,
            'th_district_id': partner.th_district_id.id if partner.th_district_id else False,
            'state_id': partner.state_id.id if partner.state_id else False,
            'country_id': partner.country_id.code if partner.country_id else False,

            'th_street': partner.th_street.strip() if partner.th_street else False,
            'th_ward_permanent_id': partner.th_ward_permanent_id.id if partner.th_ward_permanent_id else False,
            'th_district_permanent_id': partner.th_district_permanent_id.id if partner.th_district_permanent_id else False,
            'th_state_id': partner.th_state_id.id if partner.th_state_id else False,
            'th_country_id': partner.th_country_id.code if partner.th_country_id else False,
            'th_ethnicity_id': partner.th_ethnicity_id.id if partner.th_ethnicity_id else False,
            'th_religion_id': partner.th_religion_id.id if partner.th_religion_id else False,
            'th_module_ids': partner.th_module_ids.ids if partner.th_module_ids else False,
            'th_apm_contact_trait_ids': partner.th_apm_contact_trait_ids.ids if hasattr(partner, 'th_apm_contact_trait_ids') and partner.th_apm_contact_trait_ids else False,

            # Tab bán hàng và mua hàng
            'customer_rank': partner.customer_rank if hasattr(partner, 'customer_rank') and partner.customer_rank else 0,
            'supplier_rank': partner.supplier_rank if hasattr(partner, 'supplier_rank') and partner.supplier_rank else 0,
            'is_company': partner.is_company if partner.is_company else False,
            'category_id': partner.category_id.ids if partner.category_id else False,
            'user_id': partner.user_id.id if partner.user_id else False,
            'team_id': partner.team_id.id if hasattr(partner, 'team_id') and partner.team_id else False,

            # Tab kế toán
            'property_account_receivable_id': partner.property_account_receivable_id.id if hasattr(partner, 'property_account_receivable_id') and partner.property_account_receivable_id else False,
            'property_account_payable_id': partner.property_account_payable_id.id if hasattr(partner, 'property_account_payable_id') and partner.property_account_payable_id else False,
            'property_payment_term_id': partner.property_payment_term_id.id if hasattr(partner, 'property_payment_term_id') and partner.property_payment_term_id else False,
            'property_supplier_payment_term_id': partner.property_supplier_payment_term_id.id if hasattr(partner, 'property_supplier_payment_term_id') and partner.property_supplier_payment_term_id else False,

            # Ghi chú nội bộ
            'comment': partner.comment.strip() if partner.comment else False,
        }
        if action == 'update':
            automation = self.env.ref('th_sync_fastapi.th_odoo_trigger_partner_update_'+th_type_sync)
            if not automation:
                return None
            trigger_field_names = automation.trigger_field_ids.mapped('name')
            data = {field: data[field] for field in trigger_field_names if field in data}
        return data

    @api.model
    def th_trigger_update_contact(self, records, action, th_type_sync='ontime'):
        for rec in records:
            clipboard = self.env['th.clipboard'].search([('th_internal_id', '=', rec.id),
                                                         ('th_model_name', '=', self._name),
                                                         ('th_status', '=', 'success'),
                                                         ('th_system', '=', 'samp')])
            if not clipboard and action != 'update':
                action = 'update'
            th_data = self.get_contact_data_sync(rec, action, th_type_sync)
            val = {
                'name': rec.name,
                'th_type_sync': th_type_sync,
                'th_internal_id': rec.id,
                'th_data': th_data,
                'th_system': 'samp',
            }
            if action == 'delete':
                self.sudo().th_func_delete(val)
            elif th_data:
                self.sudo().th_func_create_or_update(val)